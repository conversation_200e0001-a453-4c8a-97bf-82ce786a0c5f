/**
 * CAS (Create-A-Sim) Analysis Types and Enums
 * 
 * This file contains all type definitions, interfaces, and enums
 * related to CAS Part analysis and categorization.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring
 * to follow the Single Responsibility Principle.
 */

/**
 * Detailed CAS Part information extracted from actual resource data
 */
export interface CASPartInfo {
    category: CASCategory;
    subcategory: string;
    ageGroups: AgeGroup[];
    genders: Gender[];
    clothingCategories: ClothingCategory[];
    bodyLocation: BodyLocation[];
    isAccessory: boolean;
    isHair: boolean;
    isMakeup: boolean;
    isClothing: boolean;
    tags: string[];
    description: string;
}

/**
 * CAS main categories
 */
export enum CASCategory {
    HAIR = 'hair',
    CLOTHING = 'clothing', 
    MAKEUP = 'makeup',
    ACCESSORIES = 'accessories',
    BODY_HAIR = 'body_hair',
    SKIN_DETAILS = 'skin_details',
    UNKNOWN = 'unknown'
}

/**
 * Age groups for CAS content
 */
export enum AgeGroup {
    INFANT = 'infant',
    TODDLER = 'toddler', 
    CHILD = 'child',
    TEEN = 'teen',
    YOUNG_ADULT = 'young_adult',
    ADULT = 'adult',
    ELDER = 'elder'
}

/**
 * Gender categories
 */
export enum Gender {
    MALE = 'male',
    FEMALE = 'female',
    UNISEX = 'unisex'
}

/**
 * Clothing categories for outfits
 */
export enum ClothingCategory {
    EVERYDAY = 'everyday',
    FORMAL = 'formal',
    ATHLETIC = 'athletic',
    SLEEP = 'sleep',
    PARTY = 'party',
    SWIMWEAR = 'swimwear',
    HOT_WEATHER = 'hot_weather',
    COLD_WEATHER = 'cold_weather',
    BATHING = 'bathing'
}

/**
 * Body locations for clothing and accessories
 */
export enum BodyLocation {
    HEAD = 'head',
    HAIR = 'hair',
    FACE = 'face',
    UPPER_BODY = 'upper_body',
    LOWER_BODY = 'lower_body',
    FULL_BODY = 'full_body',
    FEET = 'feet',
    HANDS = 'hands',
    NECK = 'neck',
    EARS = 'ears',
    WRISTS = 'wrists',
    RINGS = 'rings'
}

/**
 * CAS Part summary information for categorization
 */
export interface CASPartSummary {
    primaryCategory: string;
    subcategory: string;
    description: string;
}

/**
 * Display names mapping for CAS categories
 */
export const CAS_CATEGORY_DISPLAY_NAMES: Record<CASCategory, string> = {
    [CASCategory.HAIR]: 'Hair',
    [CASCategory.CLOTHING]: 'Clothing',
    [CASCategory.MAKEUP]: 'Makeup',
    [CASCategory.ACCESSORIES]: 'Accessories',
    [CASCategory.BODY_HAIR]: 'Body Hair',
    [CASCategory.SKIN_DETAILS]: 'Skin Details',
    [CASCategory.UNKNOWN]: 'Unknown CAS'
};

/**
 * Age group bit flags for parsing AgeGender values
 */
export const AGE_GROUP_FLAGS = {
    INFANT: 0x0001,
    TODDLER: 0x0002,
    CHILD: 0x0004,
    TEEN: 0x0008,
    YOUNG_ADULT: 0x0010,
    ADULT: 0x0020,
    ELDER: 0x0040
} as const;

/**
 * Gender bit flags for parsing AgeGender values
 */
export const GENDER_FLAGS = {
    MALE: 0x1000,
    FEMALE: 0x2000
} as const;

/**
 * CAS Part type constants for mapping part types to categories
 */
export const CAS_PART_TYPES = {
    HAIR: 1,
    FACE_PAINT: 2,
    TOP: 4,
    BOTTOM: 8,
    FULL_BODY: 16,
    SHOES: 32,
    ACCESSORIES: 64,
    MAKEUP: 128
} as const;

/**
 * Default fallback CAS part info for when analysis fails
 */
export const DEFAULT_CAS_PART_INFO: CASPartInfo = {
    category: CASCategory.UNKNOWN,
    subcategory: 'unknown',
    ageGroups: [AgeGroup.TEEN, AgeGroup.YOUNG_ADULT, AgeGroup.ADULT, AgeGroup.ELDER],
    genders: [Gender.UNISEX],
    clothingCategories: [ClothingCategory.EVERYDAY],
    bodyLocation: [],
    isAccessory: false,
    isHair: false,
    isMakeup: false,
    isClothing: true,
    tags: [],
    description: 'Unknown CAS content'
};

/**
 * CAS resource type identifier
 */
export const CAS_PART_RESOURCE_TYPE = 0x034AEECB;
