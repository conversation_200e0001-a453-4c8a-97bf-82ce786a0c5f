import type { AnalyzedPackage } from '../../types/analysis';
import { quickAnalysisService } from './core/QuickAnalysisService';
import { detailedAnalysisService } from './core/DetailedAnalysisService';
import { batchAnalysisService } from './batch/BatchAnalysisService';
import { conflictAnalysisService } from './conflict/ConflictAnalysisService';
import type {
    QuickAnalysisResult,
    EnhancedQuickAnalysisResult,
    DetailedAnalysisResult,
    EnhancedDetailedAnalysisResult,
    BatchAnalysisOptions,
    ConflictAnalysisResult,
    CancellationToken
} from '../../types/analysis-results';

/**
 * The PackageAnalysisService provides a high-level API for analyzing
 * Sims 4 mod files (.package and .ts4script). It orchestrates specialized
 * analysis services to provide comprehensive mod analysis capabilities.
 * 
 * This service acts as a facade, delegating to specialized services:
 * - QuickAnalysisService: Fast categorization and basic detection
 * - DetailedAnalysisService: Comprehensive resource analysis
 * - BatchAnalysisService: Concurrent processing of multiple files
 * - ConflictAnalysisService: Conflict detection and TGI analysis
 * 
 * REFACTORED: This service is now much smaller and follows the Single Responsibility Principle
 * by delegating to specialized services rather than handling everything internally.
 */
export class PackageAnalysisService {

    // ========================================
    // QUICK ANALYSIS METHODS
    // ========================================

    /**
     * Async quick analysis for immediate UI feedback
     * Delegates to QuickAnalysisService for specialized handling
     */
    public async quickAnalyzeAsync(buffer: Buffer, filePath: string, cancellationToken?: CancellationToken): Promise<EnhancedQuickAnalysisResult> {
        return quickAnalysisService.analyzeAsync(buffer, filePath, cancellationToken);
    }

    /**
     * Quick analysis for immediate UI feedback (synchronous)
     * Delegates to QuickAnalysisService for specialized handling
     */
    public quickAnalyze(buffer: Buffer, filePath: string): QuickAnalysisResult {
        return quickAnalysisService.analyze(buffer, filePath);
    }

    // ========================================
    // DETAILED ANALYSIS METHODS
    // ========================================

    /**
     * Async detailed analysis using selective resource loading
     * Delegates to DetailedAnalysisService for specialized handling
     */
    public async detailedAnalyzeAsync(buffer: Buffer, filePath: string, cancellationToken?: CancellationToken): Promise<EnhancedDetailedAnalysisResult> {
        const quickResult = await this.quickAnalyzeAsync(buffer, filePath, cancellationToken);
        return detailedAnalysisService.analyzeAsync(buffer, filePath, quickResult, cancellationToken);
    }

    /**
     * Detailed analysis using selective resource loading (synchronous)
     * Delegates to DetailedAnalysisService for specialized handling
     */
    public detailedAnalyze(buffer: Buffer, filePath: string): DetailedAnalysisResult {
        const quickResult = this.quickAnalyze(buffer, filePath);
        return detailedAnalysisService.analyze(buffer, filePath, quickResult);
    }

    // ========================================
    // BATCH ANALYSIS METHODS
    // ========================================

    /**
     * Analyzes multiple files with progress reporting and cancellation support
     * Delegates to BatchAnalysisService for specialized handling
     */
    public async analyzeBatchAsync(
        files: { buffer: Buffer; filePath: string }[],
        options: BatchAnalysisOptions = {}
    ): Promise<EnhancedDetailedAnalysisResult[]> {
        return batchAnalysisService.analyzeBatch(files, options);
    }

    /**
     * Analyzes multiple files and provides batch organization suggestions (synchronous)
     * Maintained for backward compatibility - converts to async internally
     */
    public analyzeBatch(files: { buffer: Buffer; filePath: string }[]): AnalyzedPackage[] {
        // For backward compatibility, we'll run the async version synchronously
        // This is not ideal but maintains the existing API
        const results: AnalyzedPackage[] = [];

        files.forEach(file => {
            try {
                const result = this.analyzePackage(file.buffer, file.filePath);
                results.push(result);
            } catch (error) {
                console.error(`Failed to analyze ${file.filePath}:`, error);
                // Add error result for failed files
                const errorMessage = error instanceof Error ? error.message : String(error);
                results.push({
                    filePath: file.filePath,
                    fileType: 'unknown' as any,
                    category: 'UNKNOWN' as any,
                    subcategory: 'error',
                    fileSize: file.buffer.length,
                    resourceCount: 0,
                    isOverride: false,
                    resources: [],
                    dependencies: [],
                    conflicts: [],
                    metadata: { error: errorMessage }
                } as AnalyzedPackage);
            }
        });

        return results;
    }

    // ========================================
    // CONFLICT ANALYSIS METHODS
    // ========================================

    /**
     * Analyzes files for conflicts using optimized S4TK methods
     * Delegates to ConflictAnalysisService for specialized handling
     */
    public analyzeForConflicts(buffers: { buffer: Buffer; filePath: string }[]): ConflictAnalysisResult[] {
        return conflictAnalysisService.analyzeForConflicts(buffers);
    }

    /**
     * Analyzes files for conflicts asynchronously
     * Delegates to ConflictAnalysisService for specialized handling
     */
    public async analyzeForConflictsAsync(buffers: { buffer: Buffer; filePath: string }[]): Promise<ConflictAnalysisResult[]> {
        return conflictAnalysisService.analyzeForConflictsAsync(buffers);
    }

    // ========================================
    // LEGACY COMPATIBILITY METHODS
    // ========================================

    /**
     * Legacy method maintained for backward compatibility
     * Uses the new detailed analysis internally
     */
    public analyzePackage(buffer: Buffer, filePath: string): AnalyzedPackage {
        const result = this.detailedAnalyze(buffer, filePath);

        // Convert to legacy format if needed
        return {
            filePath: result.filePath,
            fileType: result.fileType,
            category: result.category,
            subcategory: result.subcategory,
            fileSize: result.fileSize,
            resourceCount: result.resourceCount,
            isOverride: result.isOverride,
            resources: result.resources,
            dependencies: result.dependencies,
            conflicts: result.conflicts,
            metadata: result.metadata
        };
    }

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Creates cancellation token for async operations
     */
    public createCancellationToken(): CancellationToken {
        return batchAnalysisService.createCancellationToken();
    }

    /**
     * Generates conflict report with detailed information
     */
    public generateConflictReport(results: ConflictAnalysisResult[]) {
        return conflictAnalysisService.generateConflictReport(results);
    }
}