# Simonitor - Next Session Instructions

## 🚨 **CRITICAL ISSUE TO FIX**

The Simonitor Sims 4 mod management application has a **UI integration problem**. The app launches but shows the old FileUpload interface instead of the new ModDashboard that was built. The analysis engine works perfectly (99% Resource Intelligence coverage proven), but the frontend is not properly integrated.

## 📋 **IMMEDIATE TASK**

**Fix App.vue to display ModDashboard instead of FileUpload interface**

The core issue is that App.vue is not properly switching to the ModDashboard view when analysis begins. All backend systems are functional, but the frontend is stuck on the legacy interface.

## 📚 **REQUIRED READING (Read These First)**

1. **DEVELOPMENT_STATUS.md** - Complete status of all systems (working vs broken)
2. **NEXT_STEPS.md** - Detailed step-by-step fix plan
3. **README.md** - Updated project overview with current status
4. **src/renderer/App.vue** - Current main app component (needs fixing)
5. **src/renderer/components/ModDashboard.vue** - New dashboard (built but not integrated)

## 🔍 **PROVEN WORKING SYSTEMS (Don't Touch These)**

### **Analysis Engine (99% Coverage Proven)**
- **PackageAnalysisService**: Enhanced with `detailedAnalyzeAsync()` method
- **ResourceIntelligenceAnalyzer**: 99% coverage (1,332/1,339 files)
- **ScriptIntelligenceAnalyzer**: 100% coverage (65/65 scripts)
- **QualityAssessmentAnalyzer**: 99% coverage with 78/100 average score
- **Author Detection**: 97% accuracy across 134 unique creators

### **Enhanced Electron Main Process (100% Functional)**
- **IPC Handlers**: `analyze-package`, `analyze-mods-folder`, `select-mods-folder`, `export-results`
- **Folder Analysis**: `analyzeModsFolder()` processes 6 files/second
- **Export System**: JSON/CSV export with native dialogs

### **Complete UI Components (Built but Not Integrated)**
- **ModDashboard.vue**: Main dashboard with stats, search, filtering
- **ModCard.vue**: Expandable cards with intelligence display
- **All Intelligence Display Components**: Resource, Script, Quality displays
- **Design System**: Complete CSS framework with Apple + Sims 4 aesthetics

## 🎯 **SPECIFIC TECHNICAL TASK**

### **Primary Objective**
Modify `src/renderer/App.vue` to:
1. Show FileUpload initially for file/folder selection
2. Switch to ModDashboard when analysis begins
3. Pass analysis results to ModDashboard components
4. Handle view state management properly

### **Expected Code Structure**
```vue
<template>
  <div id="app">
    <!-- Show FileUpload when no analysis results -->
    <FileUpload 
      v-if="!hasAnalysisResults && !isAnalyzing"
      @folder-selected="handleFolderSelected"
    />
    
    <!-- Show ModDashboard when analysis starts/results exist -->
    <ModDashboard
      v-else
      :analysis-results="analysisResults"
      :is-analyzing="isAnalyzing"
      :total-files="totalFiles"
      :processed-files="processedFiles"
    />
  </div>
</template>
```

## 🔧 **IMPLEMENTATION STEPS**

### **Step 1: Examine Current State**
1. Use `view` tool to examine `src/renderer/App.vue`
2. Check what's currently preventing ModDashboard from showing
3. Identify missing imports or logic issues

### **Step 2: Fix Integration**
1. Import ModDashboard component if not already imported
2. Add reactive state for analysis results and progress
3. Implement conditional rendering logic
4. Connect folder selection to analysis trigger

### **Step 3: Test Integration**
1. Launch app with `npm run dev`
2. Verify FileUpload shows initially
3. Test folder selection triggers ModDashboard
4. Confirm analysis results display correctly

### **Step 4: Validate Features**
1. Test intelligence data display (Resource, Script, Quality)
2. Verify filtering and search functionality
3. Test export functionality
4. Check performance with multiple files

## 📊 **SUCCESS CRITERIA**

- ✅ App launches showing FileUpload interface
- ✅ Folder selection switches to ModDashboard
- ✅ Analysis results display with intelligence indicators
- ✅ All dashboard features work (search, filter, export)
- ✅ Performance maintains proven 6 files/second throughput

## ⚠️ **IMPORTANT CONSTRAINTS**

### **DO NOT MODIFY THESE (They Work Perfectly)**
- Any files in `src/services/analysis/` - Analysis engine is proven functional
- `src/main/index.ts` - Electron main process works correctly
- `src/preload/index.ts` - IPC bridge is functional
- Individual component files in `src/renderer/components/` - All built correctly

### **FOCUS ONLY ON**
- `src/renderer/App.vue` - Main integration point
- View state management and component switching
- Data flow from analysis to dashboard components

## 🧪 **TESTING APPROACH**

### **Quick Test**
1. Launch app: Should show FileUpload
2. Click "Select Mods Folder": Should switch to ModDashboard
3. Analysis should run and display results

### **Full Test**
1. Test with small folder (5-10 mods)
2. Verify all intelligence types display
3. Test filtering and search
4. Test export functionality

## 📁 **KEY FILE LOCATIONS**

- **Main App**: `src/renderer/App.vue` (NEEDS FIXING)
- **Dashboard**: `src/renderer/components/ModDashboard.vue` (READY TO USE)
- **Analysis Service**: `src/services/analysis/PackageAnalysisService.ts` (WORKING)
- **Main Process**: `src/main/index.ts` (WORKING)
- **Design System**: `src/renderer/styles/simonitor-design-system.css` (COMPLETE)

## 🎯 **EXPECTED OUTCOME**

After fixing the integration, Simonitor will:
1. Launch with proper file selection interface
2. Switch to advanced dashboard when analysis begins
3. Display comprehensive mod intelligence (99% coverage)
4. Provide professional-grade filtering and export capabilities
5. Handle large mod collections efficiently (tested with 1,339 mods)

## 💡 **QUICK WIN STRATEGY**

Start with the simplest possible fix:
1. Examine App.vue current state
2. Add ModDashboard import if missing
3. Implement basic conditional rendering
4. Test folder selection triggers view switch
5. Gradually verify all features work

This approach ensures rapid progress while leveraging all the proven working systems.
