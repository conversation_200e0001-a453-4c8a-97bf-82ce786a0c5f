import type { ResourceEntry } from '@s4tk/models/types';
import { BinaryResourceType } from '@s4tk/models/enums';
import { URT } from '../../constants/unifiedResourceTypes';
import { errorHandler } from './ErrorHandler';

/**
 * Resource analysis result from specialized analyzers
 */
export interface SpecializedResourceAnalysis {
    resourceKey: string;
    resourceType: string;
    analysisType: 'simdata' | 'stringtable' | 'tuning' | 'image' | 'generic';
    metadata: any;
    preview?: string;
    issues: string[];
    processingTime?: number;
}

/**
 * Resource validation result
 */
export interface ResourceValidationResult {
    isValid: boolean;
    issues: string[];
    warnings: string[];
}

/**
 * Interface for specialized resource analyzers
 */
export interface ISpecializedResourceAnalyzer {
    canAnalyze(resourceType: number): boolean;
    analyze(entry: ResourceEntry): Promise<SpecializedResourceAnalysis>;
    validate(entry: ResourceEntry): ResourceValidationResult;
    getAnalysisType(): string;
}

/**
 * Service for coordinating specialized resource parsing and analysis
 */
export class ResourceParser {
    private analyzers: Map<number, ISpecializedResourceAnalyzer> = new Map();
    private fallbackAnalyzer: ISpecializedResourceAnalyzer;
    
    constructor() {
        this.fallbackAnalyzer = new GenericResourceAnalyzer();
    }
    
    /**
     * Registers a specialized analyzer for specific resource types
     * @param resourceTypes - Array of resource types this analyzer handles
     * @param analyzer - The analyzer instance
     */
    public registerAnalyzer(resourceTypes: number[], analyzer: ISpecializedResourceAnalyzer): void {
        resourceTypes.forEach(type => {
            this.analyzers.set(type, analyzer);
        });
    }
    
    /**
     * Analyzes a resource using the appropriate specialized analyzer
     * @param entry - Resource entry to analyze
     * @returns Promise resolving to analysis result
     */
    public async analyzeResource(entry: ResourceEntry): Promise<SpecializedResourceAnalysis> {
        const startTime = Date.now();
        
        try {
            const analyzer = this.getAnalyzer(entry.key.type);
            const result = await analyzer.analyze(entry);
            
            // Add processing time
            result.processingTime = Date.now() - startTime;
            
            return result;
            
        } catch (error) {
            const errorInfo = errorHandler.handleS4TKError(
                error,
                `analyzeResource(${entry.key.type})`,
                entry.id?.toString() || 'unknown'
            );
            
            // Return error analysis result
            return {
                resourceKey: entry.id?.toString() || 'unknown',
                resourceType: this.getResourceTypeName(entry.key.type),
                analysisType: 'generic',
                metadata: { error: errorInfo.message },
                issues: [errorInfo.message],
                processingTime: Date.now() - startTime
            };
        }
    }
    
    /**
     * Validates a resource using the appropriate analyzer
     * @param entry - Resource entry to validate
     * @returns Validation result
     */
    public validateResource(entry: ResourceEntry): ResourceValidationResult {
        try {
            const analyzer = this.getAnalyzer(entry.key.type);
            return analyzer.validate(entry);
            
        } catch (error) {
            errorHandler.handleS4TKError(
                error,
                `validateResource(${entry.key.type})`,
                entry.id?.toString() || 'unknown'
            );
            
            return {
                isValid: false,
                issues: [`Validation failed: ${error.message}`],
                warnings: []
            };
        }
    }
    
    /**
     * Analyzes multiple resources in batch
     * @param entries - Array of resource entries
     * @returns Promise resolving to array of analysis results
     */
    public async analyzeResourcesBatch(entries: ResourceEntry[]): Promise<SpecializedResourceAnalysis[]> {
        const results: SpecializedResourceAnalysis[] = [];
        
        // Process resources in parallel with concurrency limit
        const concurrencyLimit = 5;
        const chunks = this.chunkArray(entries, concurrencyLimit);
        
        for (const chunk of chunks) {
            const chunkResults = await Promise.all(
                chunk.map(entry => this.analyzeResource(entry))
            );
            results.push(...chunkResults);
        }
        
        return results;
    }
    
    /**
     * Gets the appropriate analyzer for a resource type
     * @param resourceType - Resource type number
     * @returns Analyzer instance
     */
    private getAnalyzer(resourceType: number): ISpecializedResourceAnalyzer {
        return this.analyzers.get(resourceType) || this.fallbackAnalyzer;
    }
    
    /**
     * Gets human-readable resource type name
     * @param type - Resource type number
     * @returns Resource type name
     */
    private getResourceTypeName(type: number): string {
        // Try S4TK's BinaryResourceType enum first
        const s4tkName = Object.entries(BinaryResourceType)
            .find(([, value]) => value === type)?.[0];
        
        if (s4tkName) {
            return s4tkName.replace(/([A-Z])/g, ' $1').trim();
        }
        
        // Fall back to URT system
        const entry = Object.entries(URT).find(([, value]) => value === type);
        if (entry) {
            return entry[0].replace(/([A-Z])/g, ' $1').trim();
        }
        
        return `Unknown (0x${type.toString(16).toUpperCase().padStart(8, '0')})`;
    }
    
    /**
     * Chunks an array into smaller arrays
     * @param array - Array to chunk
     * @param size - Chunk size
     * @returns Array of chunks
     */
    private chunkArray<T>(array: T[], size: number): T[][] {
        const chunks: T[][] = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }
}

/**
 * Generic resource analyzer for unsupported resource types
 */
class GenericResourceAnalyzer implements ISpecializedResourceAnalyzer {
    
    canAnalyze(resourceType: number): boolean {
        return true; // Can analyze any resource type generically
    }
    
    async analyze(entry: ResourceEntry): Promise<SpecializedResourceAnalysis> {
        const resourceType = this.getResourceTypeName(entry.key.type);
        
        return {
            resourceKey: entry.id?.toString() || 'unknown',
            resourceType,
            analysisType: 'generic',
            metadata: {
                type: entry.key.type,
                group: entry.key.group,
                instance: entry.key.instance.toString(),
                bufferSize: entry.value?.buffer?.length || 0,
                compressionType: entry.value?.compressionType,
                isCompressed: entry.value?.compressionType !== 0x0000
            },
            issues: []
        };
    }
    
    validate(entry: ResourceEntry): ResourceValidationResult {
        const issues: string[] = [];
        const warnings: string[] = [];
        
        // Basic validation
        if (!entry.key || typeof entry.key.type !== 'number') {
            issues.push('Invalid resource key');
        }
        
        if (!entry.value?.buffer || entry.value.buffer.length === 0) {
            issues.push('Empty or missing resource buffer');
        }
        
        // Check for unusually large resources
        if (entry.value?.buffer && entry.value.buffer.length > 50 * 1024 * 1024) {
            warnings.push(`Large resource: ${entry.value.buffer.length} bytes`);
        }
        
        return {
            isValid: issues.length === 0,
            issues,
            warnings
        };
    }
    
    getAnalysisType(): string {
        return 'generic';
    }
    
    private getResourceTypeName(type: number): string {
        const entry = Object.entries(BinaryResourceType)
            .find(([, value]) => value === type);
        
        if (entry) {
            return entry[0].replace(/([A-Z])/g, ' $1').trim();
        }
        
        return `Unknown (0x${type.toString(16).toUpperCase().padStart(8, '0')})`;
    }
}

// Export singleton instance
export const resourceParser = new ResourceParser();