import { ObjectCategory, ObjectFunction, RoomType, ObjectStyle } from './ObjectTypes';

/**
 * Service responsible for detecting object categories from various data sources
 */
export class ObjectCategoryDetector {

    /**
     * Determines object category from filename patterns
     */
    public static detectCategoryFromFilename(filename: string): ObjectCategory {
        const lowerFilename = filename.toLowerCase();

        // Seating detection
        if (this.matchesPatterns(lowerFilename, [
            'chair', 'seat', 'sofa', 'couch', 'bench', 'stool', 'armchair', 'loveseat'
        ])) {
            return ObjectCategory.SEATING;
        }

        // Surfaces detection
        if (this.matchesPatterns(lowerFilename, [
            'table', 'desk', 'counter', 'surface', 'nightstand', 'dresser', 'shelf'
        ])) {
            return ObjectCategory.SURFACES;
        }

        // Storage detection
        if (this.matchesPatterns(lowerFilename, [
            'storage', 'cabinet', 'wardrobe', 'closet', 'chest', 'drawer', 'bookcase'
        ])) {
            return ObjectCategory.STORAGE;
        }

        // Lighting detection
        if (this.matchesPatterns(lowerFilename, [
            'lamp', 'light', 'lighting', 'chandelier', 'sconce', 'fixture'
        ])) {
            return ObjectCategory.LIGHTING;
        }

        // Appliances detection
        if (this.matchesPatterns(lowerFilename, [
            'stove', 'oven', 'fridge', 'refrigerator', 'microwave', 'dishwasher', 'washer', 'dryer'
        ])) {
            return ObjectCategory.APPLIANCES;
        }

        // Plumbing detection
        if (this.matchesPatterns(lowerFilename, [
            'toilet', 'sink', 'bathtub', 'shower', 'basin', 'faucet'
        ])) {
            return ObjectCategory.PLUMBING;
        }

        // Electronics detection
        if (this.matchesPatterns(lowerFilename, [
            'tv', 'television', 'computer', 'stereo', 'radio', 'phone', 'tablet'
        ])) {
            return ObjectCategory.ELECTRONICS;
        }

        // Plants detection
        if (this.matchesPatterns(lowerFilename, [
            'plant', 'flower', 'tree', 'bush', 'garden', 'pot'
        ])) {
            return ObjectCategory.PLANTS;
        }

        // Decorative detection
        if (this.matchesPatterns(lowerFilename, [
            'decor', 'decoration', 'art', 'painting', 'sculpture', 'vase', 'ornament'
        ])) {
            return ObjectCategory.DECORATIVE;
        }

        return ObjectCategory.UNKNOWN;
    }

    /**
     * Determines object function from category and context
     */
    public static detectFunction(category: ObjectCategory, context?: any): ObjectFunction {
        switch (category) {
            case ObjectCategory.SEATING:
                return ObjectFunction.SEATING;
            case ObjectCategory.STORAGE:
                return ObjectFunction.STORAGE;
            case ObjectCategory.LIGHTING:
                return ObjectFunction.LIGHTING;
            case ObjectCategory.APPLIANCES:
                return ObjectFunction.COOKING;
            case ObjectCategory.PLUMBING:
                return ObjectFunction.HYGIENE;
            case ObjectCategory.ELECTRONICS:
                return ObjectFunction.ENTERTAINMENT;
            case ObjectCategory.DECORATIVE:
                return ObjectFunction.DECORATION;
            default:
                return ObjectFunction.UNKNOWN;
        }
    }

    /**
     * Determines room assignment from category and filename
     */
    public static detectRoomAssignment(category: ObjectCategory, filename: string): RoomType[] {
        const lowerFilename = filename.toLowerCase();
        const rooms: RoomType[] = [];

        // Kitchen-specific items
        if (this.matchesPatterns(lowerFilename, ['kitchen', 'stove', 'oven', 'fridge', 'counter']) ||
            category === ObjectCategory.APPLIANCES) {
            rooms.push(RoomType.KITCHEN);
        }

        // Bathroom-specific items
        if (this.matchesPatterns(lowerFilename, ['bathroom', 'toilet', 'shower', 'bathtub']) ||
            category === ObjectCategory.PLUMBING) {
            rooms.push(RoomType.BATHROOM);
        }

        // Bedroom-specific items
        if (this.matchesPatterns(lowerFilename, ['bedroom', 'bed', 'nightstand', 'dresser'])) {
            rooms.push(RoomType.BEDROOM);
        }

        // Living room items
        if (this.matchesPatterns(lowerFilename, ['living', 'sofa', 'couch', 'tv', 'entertainment'])) {
            rooms.push(RoomType.LIVING_ROOM);
        }

        // Office items
        if (this.matchesPatterns(lowerFilename, ['office', 'desk', 'computer', 'chair'])) {
            rooms.push(RoomType.OFFICE);
        }

        // Outdoor items
        if (this.matchesPatterns(lowerFilename, ['outdoor', 'garden', 'patio', 'deck'])) {
            rooms.push(RoomType.OUTDOOR);
        }

        // If no specific room detected, assign based on category
        if (rooms.length === 0) {
            switch (category) {
                case ObjectCategory.SEATING:
                case ObjectCategory.SURFACES:
                case ObjectCategory.LIGHTING:
                case ObjectCategory.DECORATIVE:
                    rooms.push(RoomType.ANY_ROOM);
                    break;
                default:
                    rooms.push(RoomType.ANY_ROOM);
            }
        }

        return rooms;
    }

    /**
     * Detects object style from filename and context
     */
    public static detectStyle(filename: string, context?: any): ObjectStyle[] {
        const lowerFilename = filename.toLowerCase();
        const styles: ObjectStyle[] = [];

        if (this.matchesPatterns(lowerFilename, ['modern', 'contemporary', 'sleek'])) {
            styles.push(ObjectStyle.MODERN);
        }

        if (this.matchesPatterns(lowerFilename, ['traditional', 'classic', 'vintage'])) {
            styles.push(ObjectStyle.TRADITIONAL);
        }

        if (this.matchesPatterns(lowerFilename, ['rustic', 'country', 'farmhouse'])) {
            styles.push(ObjectStyle.RUSTIC);
        }

        if (this.matchesPatterns(lowerFilename, ['industrial', 'metal', 'steel'])) {
            styles.push(ObjectStyle.INDUSTRIAL);
        }

        if (this.matchesPatterns(lowerFilename, ['minimal', 'simple', 'clean'])) {
            styles.push(ObjectStyle.MINIMALIST);
        }

        if (this.matchesPatterns(lowerFilename, ['luxury', 'premium', 'elegant'])) {
            styles.push(ObjectStyle.LUXURY);
        }

        if (styles.length === 0) {
            styles.push(ObjectStyle.UNKNOWN);
        }

        return styles;
    }

    /**
     * Helper method to check if filename matches any of the given patterns
     */
    private static matchesPatterns(filename: string, patterns: string[]): boolean {
        return patterns.some(pattern => filename.includes(pattern));
    }

    /**
     * Determines if object is decorative based on category and context
     */
    public static isDecorative(category: ObjectCategory): boolean {
        return category === ObjectCategory.DECORATIVE || 
               category === ObjectCategory.PLANTS ||
               category === ObjectCategory.LIGHTING;
    }

    /**
     * Determines if object is functional based on category
     */
    public static isFunctional(category: ObjectCategory): boolean {
        return category === ObjectCategory.SEATING ||
               category === ObjectCategory.SURFACES ||
               category === ObjectCategory.STORAGE ||
               category === ObjectCategory.APPLIANCES ||
               category === ObjectCategory.PLUMBING ||
               category === ObjectCategory.ELECTRONICS;
    }
}