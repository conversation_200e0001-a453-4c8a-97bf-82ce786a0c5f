<template>
  <div class="mod-table">
    <table class="table">
      <thead>
        <tr>
          <th @click="$emit('sort', 'fileName')" class="sortable">
            Name
            <ChevronUpDownIcon class="sort-icon" />
          </th>
          <th @click="$emit('sort', 'author')" class="sortable">
            Author
            <ChevronUpDownIcon class="sort-icon" />
          </th>
          <th @click="$emit('sort', 'fileSize')" class="sortable">
            Size
            <ChevronUpDownIcon class="sort-icon" />
          </th>
          <th>Type</th>
          <th>Intelligence</th>
          <th @click="$emit('sort', 'qualityScore')" class="sortable">
            Quality
            <ChevronUpDownIcon class="sort-icon" />
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="mod in mods" :key="mod.fileName" class="table-row">
          <td class="name-cell">
            <div class="name-content">
              <span class="name">{{ mod.fileName }}</span>
              <span v-if="mod.version" class="version">v{{ mod.version }}</span>
            </div>
          </td>
          <td class="author-cell">
            {{ mod.author || '-' }}
          </td>
          <td class="size-cell">
            {{ formatFileSize(mod.fileSize) }}
          </td>
          <td class="type-cell">
            <span class="file-type" :class="`type-${mod.fileExtension.slice(1)}`">
              {{ mod.fileExtension }}
            </span>
          </td>
          <td class="intelligence-cell">
            <IntelligenceIndicator
              v-if="mod.hasResourceIntelligence"
              :type="mod.intelligenceType"
              :quality-score="mod.qualityScore"
              :risk-level="mod.riskLevel"
              size="sm"
            />
            <span v-else class="no-intelligence">-</span>
          </td>
          <td class="quality-cell">
            <QualityBadge
              v-if="mod.qualityScore"
              :score="mod.qualityScore"
              size="sm"
            />
            <span v-else class="no-quality">-</span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { ChevronUpDownIcon } from '@heroicons/vue/24/outline';
import IntelligenceIndicator from './IntelligenceIndicator.vue';
import QualityBadge from './QualityBadge.vue';

defineProps<{
  mods: any[];
  sortField?: string;
}>();

defineEmits<{
  sort: [field: string];
}>();

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
</script>

<style scoped>
.mod-table {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table th {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  font-weight: var(--font-semibold);
  text-align: left;
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-light);
  white-space: nowrap;
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color var(--duration-150) var(--ease-out);
  position: relative;
}

.table th.sortable:hover {
  background: var(--bg-tertiary);
}

.sort-icon {
  width: 14px;
  height: 14px;
  margin-left: var(--space-1);
  opacity: 0.5;
}

.table td {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
}

.table-row:hover {
  background: var(--bg-secondary);
}

.name-cell {
  max-width: 300px;
}

.name-content {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.name {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.version {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.author-cell {
  color: var(--text-accent);
  font-weight: var(--font-medium);
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.size-cell {
  font-family: var(--font-family-mono);
  color: var(--text-secondary);
  text-align: right;
  min-width: 80px;
}

.type-cell {
  min-width: 80px;
}

.file-type {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
}

.file-type.type-package {
  background: var(--sims-blue-bg);
  color: var(--sims-blue);
}

.file-type.type-ts4script {
  background: var(--sims-purple-bg);
  color: var(--sims-purple);
}

.intelligence-cell {
  min-width: 150px;
}

.quality-cell {
  min-width: 100px;
}

.no-intelligence,
.no-quality {
  color: var(--text-tertiary);
  font-style: italic;
}
</style>
